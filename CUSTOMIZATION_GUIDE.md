# دليل التخصيص المتقدم - متجر نيسا 🎨

هذا الدليل يساعدك في تخصيص المتجر بشكل متقدم وإضافة ميزات جديدة.

## 🎯 تخصيص سريع

### تغيير اسم المتجر والشعار

1. **في جميع ملفات HTML:**
   - ابحثي عن `نيسا` وغيريه إلى اسم متجرك
   - ابحثي عن `عالم الأناقة النسائية` وغيري الشعار

2. **في عنوان الصفحات:**
   ```html
   <title>اسم متجرك - وصف مختصر</title>
   ```

### تغيير معلومات التواصل

**رقم واتساب:**
- ابحثي عن `966500000000` في جميع الملفات
- غيري إلى رقمك (بدون + أو 00)

**البريد الإلكتروني:**
- ابحثي عن `<EMAIL>`
- غيري إلى بريدك الإلكتروني

**الحسابات الاجتماعية:**
في ملفات HTML، ابحثي عن:
```html
<div class="social-links">
    <a href="#"><i class="fab fa-instagram"></i></a>
    <a href="#"><i class="fab fa-twitter"></i></a>
    <a href="#"><i class="fab fa-snapchat"></i></a>
</div>
```
وغيري `#` إلى روابط حساباتك.

## 🎨 تخصيص الألوان والتصميم

### تغيير نظام الألوان

**الطريقة السهلة:**
1. افتحي `css/style.css`
2. في بداية الملف، أضيفي:
```css
:root {
    --primary-color: #ff6b9d;      /* اللون الرئيسي */
    --primary-dark: #c44569;       /* اللون الداكن */
    --secondary-color: #ffeef8;    /* لون الخلفية */
    --text-color: #2c3e50;         /* لون النص */
    --light-gray: #7f8c8d;         /* الرمادي الفاتح */
}
```

3. استبدلي الألوان في الملف:
   - `#ff6b9d` → `var(--primary-color)`
   - `#c44569` → `var(--primary-dark)`

### ألوان مقترحة للمتاجر النسائية:

**الوردي الكلاسيكي:**
```css
--primary-color: #ff69b4;
--primary-dark: #e91e63;
```

**البنفسجي الأنيق:**
```css
--primary-color: #9c27b0;
--primary-dark: #7b1fa2;
```

**الذهبي الفاخر:**
```css
--primary-color: #ffd700;
--primary-dark: #ffb300;
```

**الأزرق الناعم:**
```css
--primary-color: #87ceeb;
--primary-dark: #4682b4;
```

## 📝 إضافة محتوى جديد

### إضافة قسم جديد في الصفحة الرئيسية

```html
<section class="new-section">
    <div class="container">
        <h2 class="section-title">عنوان القسم الجديد</h2>
        <p class="section-subtitle">وصف القسم</p>
        <div class="content">
            <!-- محتوى القسم -->
        </div>
    </div>
</section>
```

### إضافة صفحة جديدة

1. انسخي `products.html`
2. غيري الاسم إلى `new-page.html`
3. غيري المحتوى حسب الحاجة
4. أضيفي رابط في القائمة:
```html
<li><a href="new-page.html" class="nav-link">الصفحة الجديدة</a></li>
```

## 🛍️ إدارة المنتجات

### إضافة فئة منتجات جديدة

1. **في HTML:**
```html
<option value="new-category">الفئة الجديدة</option>
```

2. **في المنتجات:**
```html
<div class="product-card" data-category="new-category" data-price="199">
```

### إضافة خصائص للمنتجات

**إضافة أحجام:**
```html
<div class="product-sizes">
    <span class="size-option">S</span>
    <span class="size-option">M</span>
    <span class="size-option">L</span>
    <span class="size-option">XL</span>
</div>
```

**CSS للأحجام:**
```css
.product-sizes {
    display: flex;
    gap: 5px;
    margin-bottom: 1rem;
}

.size-option {
    padding: 5px 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.size-option:hover {
    background: var(--primary-color);
    color: white;
}
```

## 📱 تحسينات الجوال

### إضافة قائمة جانبية للجوال

```css
@media (max-width: 768px) {
    .mobile-sidebar {
        position: fixed;
        top: 0;
        right: -300px;
        width: 300px;
        height: 100vh;
        background: white;
        box-shadow: -2px 0 10px rgba(0,0,0,0.1);
        transition: right 0.3s ease;
        z-index: 1001;
    }
    
    .mobile-sidebar.active {
        right: 0;
    }
}
```

## 🔧 ميزات متقدمة

### إضافة نظام التقييم

**HTML:**
```html
<div class="product-rating">
    <div class="stars">
        <i class="fas fa-star"></i>
        <i class="fas fa-star"></i>
        <i class="fas fa-star"></i>
        <i class="fas fa-star"></i>
        <i class="far fa-star"></i>
    </div>
    <span class="rating-text">(4.0 من 5)</span>
</div>
```

**CSS:**
```css
.product-rating {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 1rem;
}

.stars {
    color: #ffd700;
}

.rating-text {
    color: #7f8c8d;
    font-size: 0.9rem;
}
```

### إضافة عداد المنتجات في المخزون

**HTML:**
```html
<div class="stock-info">
    <span class="stock-count">متوفر: 5 قطع</span>
    <div class="stock-bar">
        <div class="stock-fill" style="width: 50%"></div>
    </div>
</div>
```

**CSS:**
```css
.stock-info {
    margin-bottom: 1rem;
}

.stock-count {
    font-size: 0.9rem;
    color: #27ae60;
    font-weight: 600;
}

.stock-bar {
    width: 100%;
    height: 4px;
    background: #ecf0f1;
    border-radius: 2px;
    margin-top: 5px;
}

.stock-fill {
    height: 100%;
    background: #27ae60;
    border-radius: 2px;
    transition: width 0.3s ease;
}
```

## 🎁 إضافة نظام الكوبونات

**HTML:**
```html
<div class="coupon-section">
    <h3>هل لديك كود خصم؟</h3>
    <div class="coupon-input">
        <input type="text" placeholder="أدخلي كود الخصم" id="couponCode">
        <button onclick="applyCoupon()">تطبيق</button>
    </div>
</div>
```

**JavaScript:**
```javascript
function applyCoupon() {
    const code = document.getElementById('couponCode').value;
    const validCoupons = {
        'WELCOME10': 10,
        'SUMMER20': 20,
        'VIP30': 30
    };
    
    if (validCoupons[code]) {
        alert(`تم تطبيق خصم ${validCoupons[code]}%!`);
    } else {
        alert('كود الخصم غير صحيح');
    }
}
```

## 📊 إضافة تحليلات Google Analytics

أضيفي هذا الكود قبل إغلاق `</head>`:

```html
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_TRACKING_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_TRACKING_ID');
</script>
```

## 🚀 تحسين الأداء

### ضغط الصور
- استخدمي أدوات ضغط الصور مثل TinyPNG
- احفظي الصور بصيغة WebP للمتصفحات الحديثة

### تحسين CSS
```css
/* استخدمي will-change للعناصر المتحركة */
.product-card {
    will-change: transform;
}

/* استخدمي transform بدلاً من تغيير الموقع */
.product-card:hover {
    transform: translateY(-10px);
}
```

## 🔒 الأمان

### حماية من البريد المزعج
أضيفي `rel="noopener noreferrer"` للروابط الخارجية:

```html
<a href="https://wa.me/966500000000" target="_blank" rel="noopener noreferrer">
```

### تشفير معلومات التواصل
```javascript
// تشفير بسيط لرقم الهاتف
const phoneNumber = atob('OTY2NTAwMDAwMDAw'); // مشفر
```

## 📧 إضافة نموذج اتصال

```html
<form class="contact-form" onsubmit="sendMessage(event)">
    <input type="text" placeholder="الاسم" required>
    <input type="email" placeholder="البريد الإلكتروني" required>
    <textarea placeholder="الرسالة" required></textarea>
    <button type="submit">إرسال</button>
</form>
```

```javascript
function sendMessage(event) {
    event.preventDefault();
    // إرسال الرسالة عبر البريد الإلكتروني أو خدمة أخرى
    alert('تم إرسال رسالتك بنجاح!');
}
```

---

**نصائح إضافية:**
- اختبري التغييرات على أجهزة مختلفة
- احتفظي بنسخة احتياطية قبل التعديل
- استخدمي أدوات المطور في المتصفح للاختبار
- اطلبي المساعدة عند الحاجة

💡 **تذكري:** التخصيص التدريجي أفضل من التغيير الجذري دفعة واحدة!
