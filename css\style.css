/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fafafa;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.navbar {
    padding: 1rem 0;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand .logo {
    font-size: 2rem;
    font-weight: 700;
    color: white;
    text-decoration: none;
}

.nav-brand .logo a {
    color: white;
    text-decoration: none;
}

.nav-brand .tagline {
    font-size: 0.9rem;
    color: rgba(255,255,255,0.9);
    margin-top: -5px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 25px;
}

.nav-link:hover,
.nav-link.active {
    background: rgba(255,255,255,0.2);
    transform: translateY(-2px);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: white;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #ffeef8, #f8e8ff);
    padding: 120px 0 80px;
    display: flex;
    align-items: center;
    min-height: 80vh;
}

.hero .container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: #7f8c8d;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.cta-button {
    display: inline-block;
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    color: white;
    padding: 15px 30px;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(255, 107, 157, 0.3);
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 107, 157, 0.4);
}

.hero-image img {
    width: 100%;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    color: white;
    text-align: center;
    padding: 120px 0 60px;
}

.page-header h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.page-header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Section Styles */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.section-subtitle {
    font-size: 1.2rem;
    text-align: center;
    color: #7f8c8d;
    margin-bottom: 3rem;
}

/* Featured Products */
.featured-products {
    padding: 80px 0;
    background: white;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.product-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.product-image {
    position: relative;
    overflow: hidden;
    height: 250px;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 107, 157, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.quick-view {
    background: white;
    color: #ff6b9d;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quick-view:hover {
    transform: scale(1.05);
}

.product-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 2;
}

.product-badge.new {
    background: #27ae60;
    color: white;
}

.product-badge.sale {
    background: #e74c3c;
    color: white;
}

.product-info {
    padding: 1.5rem;
}

.product-name {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.product-description {
    color: #7f8c8d;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.product-price {
    font-size: 1.4rem;
    font-weight: 700;
    color: #ff6b9d;
    margin-bottom: 1rem;
}

.old-price {
    text-decoration: line-through;
    color: #bdc3c7;
    font-size: 1rem;
    margin-right: 10px;
}

.product-colors {
    display: flex;
    gap: 8px;
    margin-bottom: 1rem;
}

.color-option {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid #ecf0f1;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.color-option:hover {
    transform: scale(1.2);
}

/* WhatsApp Button */
.whatsapp-btn {
    background: #25d366;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: 100%;
    text-decoration: none;
}

.whatsapp-btn:hover {
    background: #128c7e;
    transform: translateY(-2px);
}

.whatsapp-btn.large {
    padding: 15px 30px;
    font-size: 1.1rem;
}

/* About Section */
.about {
    padding: 80px 0;
    background: linear-gradient(135deg, #ffeef8, #f8e8ff);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.feature:hover {
    transform: translateY(-5px);
}

.feature i {
    font-size: 3rem;
    color: #ff6b9d;
    margin-bottom: 1rem;
}

.feature h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: #2c3e50;
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background: white;
}

.contact-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.contact-card {
    text-align: center;
    padding: 2rem;
    border-radius: 15px;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    border: 2px solid #ecf0f1;
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.contact-card.whatsapp {
    background: linear-gradient(135deg, #25d366, #128c7e);
    color: white;
    border-color: #25d366;
}

.contact-card.phone {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border-color: #3498db;
}

.contact-card.email {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    border-color: #e74c3c;
}

.contact-card i {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    padding: 40px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: #ff6b9d;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #ff6b9d;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #ff6b9d;
    color: white;
    border-radius: 50%;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #c44569;
    transform: translateY(-3px);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* WhatsApp Float Button */
.whatsapp-float {
    position: fixed;
    bottom: 20px;
    left: 20px;
    z-index: 1000;
}

.whatsapp-float a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background: #25d366;
    color: white;
    border-radius: 50%;
    text-decoration: none;
    font-size: 1.5rem;
    box-shadow: 0 5px 20px rgba(37, 211, 102, 0.3);
    transition: all 0.3s ease;
    animation: pulse 2s infinite;
}

.whatsapp-float a:hover {
    background: #128c7e;
    transform: scale(1.1);
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(37, 211, 102, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(37, 211, 102, 0);
    }
}

/* Filter Section */
.filter-section {
    background: white;
    padding: 20px 0;
    border-bottom: 1px solid #ecf0f1;
}

.filter-bar {
    display: flex;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-group label {
    font-weight: 600;
    color: #2c3e50;
}

.filter-group select {
    padding: 8px 15px;
    border: 2px solid #ecf0f1;
    border-radius: 25px;
    background: white;
    color: #2c3e50;
    font-family: 'Tajawal', sans-serif;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

.filter-group select:focus {
    outline: none;
    border-color: #ff6b9d;
}

.search-box {
    position: relative;
    margin-right: auto;
}

.search-box input {
    padding: 10px 40px 10px 15px;
    border: 2px solid #ecf0f1;
    border-radius: 25px;
    width: 250px;
    font-family: 'Tajawal', sans-serif;
    transition: border-color 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #ff6b9d;
}

.search-box i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #7f8c8d;
}

/* Products Section */
.products-section {
    padding: 40px 0 80px;
    background: #fafafa;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.8);
    animation: fadeIn 0.3s ease;
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    border-radius: 15px;
    width: 90%;
    max-width: 800px;
    position: relative;
    animation: slideIn 0.3s ease;
}

.close {
    position: absolute;
    top: 15px;
    left: 20px;
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1;
}

.close:hover {
    color: #ff6b9d;
}

.modal-body {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    padding: 2rem;
}

.modal-image img {
    width: 100%;
    border-radius: 10px;
}

.modal-info h2 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.modal-info p {
    color: #7f8c8d;
    margin-bottom: 1rem;
}

.modal-colors {
    display: flex;
    gap: 10px;
    margin-bottom: 1rem;
}

.modal-sizes {
    margin-bottom: 2rem;
}

.modal-sizes label {
    display: block;
    margin-bottom: 10px;
    font-weight: 600;
    color: #2c3e50;
}

.modal-sizes select {
    width: 100%;
    padding: 10px;
    border: 2px solid #ecf0f1;
    border-radius: 10px;
    font-family: 'Tajawal', sans-serif;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Shipping Page Styles */
.shipping-content {
    padding: 40px 0 80px;
    background: #fafafa;
}

.info-section {
    margin-bottom: 4rem;
}

.section-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    justify-content: center;
}

.section-header i {
    font-size: 2rem;
    color: #ff6b9d;
}

.section-header h2 {
    font-size: 2rem;
    color: #2c3e50;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.info-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.info-card:hover {
    transform: translateY(-5px);
}

.info-card h3 {
    color: #ff6b9d;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.info-card ul {
    list-style: none;
    margin-top: 1rem;
}

.info-card ul li {
    padding: 0.5rem 0;
    border-bottom: 1px solid #ecf0f1;
    color: #7f8c8d;
}

.info-card ul li:last-child {
    border-bottom: none;
}

/* Payment Methods */
.payment-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.payment-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease;
}

.payment-card:hover {
    transform: translateY(-5px);
}

.payment-icon {
    margin-bottom: 1rem;
}

.payment-icon i {
    font-size: 3rem;
    color: #ff6b9d;
}

.payment-card h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.payment-features {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 1rem;
}

.feature {
    color: #27ae60;
    font-size: 0.9rem;
}

.bank-details {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 10px;
    margin-top: 1rem;
    text-align: right;
}

.bank-details p {
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.digital-wallets {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.wallet {
    background: #ff6b9d;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
}

/* Return Policy */
.return-policy {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.policy-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.policy-card h3 {
    color: #ff6b9d;
    margin-bottom: 1rem;
}

.policy-card ul,
.policy-card ol {
    margin-right: 1rem;
    color: #7f8c8d;
}

.policy-card li {
    margin-bottom: 0.5rem;
}

/* FAQ Styles */
.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: white;
    margin-bottom: 1rem;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.faq-question {
    padding: 1.5rem;
    background: #ff6b9d;
    color: white;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background 0.3s ease;
}

.faq-question:hover {
    background: #c44569;
}

.faq-question h3 {
    margin: 0;
    font-size: 1.1rem;
}

.faq-answer {
    padding: 1.5rem;
    background: white;
    display: none;
}

.faq-answer.active {
    display: block;
}

/* Contact CTA */
.contact-cta {
    text-align: center;
    background: white;
    padding: 3rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-top: 3rem;
}

.contact-cta h2 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.contact-cta p {
    color: #7f8c8d;
    margin-bottom: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 80px;
        right: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: linear-gradient(135deg, #ff6b9d, #c44569);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: right 0.3s ease;
    }

    .nav-menu.active {
        right: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-5px, 6px);
    }

    .nav-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active span:nth-child(3) {
        transform: rotate(45deg) translate(-5px, -6px);
    }

    .hero .container {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .contact-methods {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 1.5rem;
    }

    .filter-bar {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .search-box {
        margin-right: 0;
    }

    .search-box input {
        width: 100%;
    }

    .modal-body {
        grid-template-columns: 1fr;
        padding: 1rem;
    }

    .info-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .payment-methods {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .return-policy {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .page-header h1 {
        font-size: 2rem;
    }

    .page-header p {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero {
        padding: 100px 0 60px;
    }

    .hero-title {
        font-size: 1.8rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .product-card {
        margin: 0 auto;
        max-width: 300px;
    }

    .nav-brand .logo {
        font-size: 1.5rem;
    }

    .nav-brand .tagline {
        font-size: 0.8rem;
    }

    .whatsapp-float {
        bottom: 15px;
        left: 15px;
    }

    .whatsapp-float a {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .faq-question {
        padding: 1rem;
    }

    .faq-answer {
        padding: 1rem;
    }

    .contact-cta {
        padding: 2rem 1rem;
    }
}

/* Print Styles */
@media print {
    .header,
    .whatsapp-float,
    .whatsapp-btn,
    .nav-toggle,
    .filter-section {
        display: none !important;
    }

    body {
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
    }

    .hero {
        padding: 20px 0;
    }

    .section-title {
        font-size: 18pt;
        margin-bottom: 10px;
    }

    .product-card {
        break-inside: avoid;
        margin-bottom: 20px;
    }
}

/* Accessibility Improvements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus Styles */
button:focus,
input:focus,
select:focus,
a:focus {
    outline: 2px solid #ff6b9d;
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .hero {
        background: #fff;
        color: #000;
    }

    .product-card {
        border: 2px solid #000;
    }

    .whatsapp-btn {
        border: 2px solid #000;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .whatsapp-float a {
        animation: none;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e0e0e0;
    }

    .product-card,
    .info-card,
    .payment-card,
    .policy-card,
    .faq-item {
        background: #2d2d2d;
        color: #e0e0e0;
    }

    .section-title,
    .product-name {
        color: #ff6b9d;
    }

    .product-description,
    .section-subtitle {
        color: #b0b0b0;
    }
}
