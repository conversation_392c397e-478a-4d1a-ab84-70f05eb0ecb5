// Mobile Navigation Toggle
document.addEventListener('DOMContentLoaded', function() {
    const navToggle = document.querySelector('.nav-toggle');
    const navMenu = document.querySelector('.nav-menu');

    if (navToggle && navMenu) {
        navToggle.addEventListener('click', function() {
            navToggle.classList.toggle('active');
            navMenu.classList.toggle('active');
        });

        // Close menu when clicking on a link
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                navToggle.classList.remove('active');
                navMenu.classList.remove('active');
            });
        });
    }

    // Smooth scrolling for anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = targetElement.offsetTop - headerHeight;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Product filtering and search
    initializeProductFilters();
    
    // FAQ functionality
    initializeFAQ();
    
    // Product modal functionality
    initializeProductModal();
    
    // Scroll animations
    initializeScrollAnimations();
});

// WhatsApp Contact Function
function contactWhatsApp(productName, price) {
    const phoneNumber = '966500000000'; // Replace with actual WhatsApp number
    const message = `مرحباً، أريد الاستفسار عن ${productName} بسعر ${price} ريال`;
    const whatsappURL = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(whatsappURL, '_blank');
}

// Product Filtering System
function initializeProductFilters() {
    const categoryFilter = document.getElementById('categoryFilter');
    const priceFilter = document.getElementById('priceFilter');
    const searchInput = document.getElementById('searchInput');
    const productsGrid = document.getElementById('productsGrid');

    if (!productsGrid) return;

    const products = Array.from(productsGrid.querySelectorAll('.product-card'));

    function filterProducts() {
        const categoryValue = categoryFilter ? categoryFilter.value : 'all';
        const priceValue = priceFilter ? priceFilter.value : 'all';
        const searchValue = searchInput ? searchInput.value.toLowerCase() : '';

        products.forEach(product => {
            const productCategory = product.dataset.category;
            const productPrice = parseInt(product.dataset.price);
            const productName = product.querySelector('.product-name').textContent.toLowerCase();
            const productDescription = product.querySelector('.product-description').textContent.toLowerCase();

            let showProduct = true;

            // Category filter
            if (categoryValue !== 'all' && productCategory !== categoryValue) {
                showProduct = false;
            }

            // Price filter
            if (priceValue !== 'all') {
                const [minPrice, maxPrice] = priceValue.split('-').map(p => p === '+' ? Infinity : parseInt(p));
                if (maxPrice) {
                    if (productPrice < minPrice || productPrice > maxPrice) {
                        showProduct = false;
                    }
                } else {
                    if (productPrice < minPrice) {
                        showProduct = false;
                    }
                }
            }

            // Search filter
            if (searchValue && !productName.includes(searchValue) && !productDescription.includes(searchValue)) {
                showProduct = false;
            }

            // Show/hide product with animation
            if (showProduct) {
                product.style.display = 'block';
                setTimeout(() => {
                    product.style.opacity = '1';
                    product.style.transform = 'translateY(0)';
                }, 10);
            } else {
                product.style.opacity = '0';
                product.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    product.style.display = 'none';
                }, 300);
            }
        });

        // Show "no products found" message if needed
        const visibleProducts = products.filter(p => p.style.display !== 'none');
        let noResultsMessage = productsGrid.querySelector('.no-results');
        
        if (visibleProducts.length === 0) {
            if (!noResultsMessage) {
                noResultsMessage = document.createElement('div');
                noResultsMessage.className = 'no-results';
                noResultsMessage.innerHTML = `
                    <div style="text-align: center; padding: 3rem; color: #7f8c8d;">
                        <i class="fas fa-search" style="font-size: 3rem; margin-bottom: 1rem; color: #bdc3c7;"></i>
                        <h3>لم يتم العثور على منتجات</h3>
                        <p>جربي تغيير معايير البحث أو التصفية</p>
                    </div>
                `;
                productsGrid.appendChild(noResultsMessage);
            }
            noResultsMessage.style.display = 'block';
        } else if (noResultsMessage) {
            noResultsMessage.style.display = 'none';
        }
    }

    // Add event listeners
    if (categoryFilter) categoryFilter.addEventListener('change', filterProducts);
    if (priceFilter) priceFilter.addEventListener('change', filterProducts);
    if (searchInput) {
        searchInput.addEventListener('input', debounce(filterProducts, 300));
    }
}

// Product Modal System
function initializeProductModal() {
    const modal = document.getElementById('productModal');
    if (!modal) return;

    const closeBtn = modal.querySelector('.close');
    const modalImage = document.getElementById('modalImage');
    const modalTitle = document.getElementById('modalTitle');
    const modalDescription = document.getElementById('modalDescription');
    const modalPrice = document.getElementById('modalPrice');
    const modalColors = document.getElementById('modalColors');
    const modalWhatsappBtn = document.getElementById('modalWhatsappBtn');

    // Product data
    const productData = {
        dress1: {
            name: 'فستان سهرة أنيق',
            description: 'فستان سهرة راقي بتصميم عصري يناسب المناسبات الخاصة. مصنوع من أجود الخامات مع تفاصيل أنيقة تبرز جمالك الطبيعي.',
            price: '299 ريال',
            image: 'images/dress1.jpg',
            colors: ['#000', '#8B4513', '#4169E1']
        },
        blouse1: {
            name: 'بلوزة عصرية راقية',
            description: 'بلوزة أنيقة مناسبة للعمل والمناسبات اليومية. تصميم عصري مريح يمنحك إطلالة مميزة طوال اليوم.',
            price: '149 ريال',
            image: 'images/blouse1.jpg',
            colors: ['#FFF', '#FFB6C1', '#87CEEB']
        },
        skirt1: {
            name: 'تنورة كلاسيكية',
            description: 'تنورة أنيقة بتصميم كلاسيكي يناسب جميع المناسبات. قطعة أساسية في خزانة كل امرأة عصرية.',
            price: '179 ريال',
            image: 'images/skirt1.jpg',
            colors: ['#000', '#8B4513']
        },
        pants1: {
            name: 'بنطلون أنيق',
            description: 'بنطلون عصري مريح ومناسب للعمل والخروجات. تصميم أنيق يجمع بين الراحة والأناقة.',
            price: '199 ريال',
            image: 'images/pants1.jpg',
            colors: ['#000', '#4169E1', '#8B4513']
        },
        dress2: {
            name: 'فستان كاجوال أنيق',
            description: 'فستان مريح وأنيق للاستخدام اليومي. تصميم عملي يناسب الأنشطة اليومية مع لمسة من الأناقة.',
            price: '259 ريال',
            image: 'images/dress2.jpg',
            colors: ['#FFB6C1', '#87CEEB', '#DDA0DD']
        },
        accessory1: {
            name: 'حقيبة يد أنيقة',
            description: 'حقيبة يد عصرية تكمل إطلالتك بأناقة. تصميم عملي مع مساحة كافية لجميع احتياجاتك اليومية.',
            price: '89 ريال',
            image: 'images/accessory1.jpg',
            colors: ['#000', '#8B4513', '#FFB6C1']
        }
    };

    // Open modal function
    window.openProductModal = function(productId) {
        const product = productData[productId];
        if (!product) return;

        modalImage.src = product.image;
        modalImage.alt = product.name;
        modalTitle.textContent = product.name;
        modalDescription.textContent = product.description;
        modalPrice.textContent = product.price;

        // Update colors
        modalColors.innerHTML = '';
        product.colors.forEach(color => {
            const colorSpan = document.createElement('span');
            colorSpan.className = 'color-option';
            colorSpan.style.background = color;
            modalColors.appendChild(colorSpan);
        });

        // Update WhatsApp button
        modalWhatsappBtn.onclick = () => {
            const size = document.getElementById('sizeSelect').value;
            contactWhatsApp(`${product.name} - مقاس ${size}`, product.price.replace(' ريال', ''));
        };

        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    };

    // Close modal
    function closeModal() {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }

    if (closeBtn) closeBtn.addEventListener('click', closeModal);
    
    modal.addEventListener('click', function(e) {
        if (e.target === modal) closeModal();
    });

    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && modal.style.display === 'block') {
            closeModal();
        }
    });
}

// FAQ Functionality
function initializeFAQ() {
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        const answer = item.querySelector('.faq-answer');
        const icon = question.querySelector('i');
        
        if (question && answer) {
            question.addEventListener('click', () => {
                const isActive = answer.classList.contains('active');
                
                // Close all other FAQ items
                faqItems.forEach(otherItem => {
                    const otherAnswer = otherItem.querySelector('.faq-answer');
                    const otherIcon = otherItem.querySelector('.faq-question i');
                    otherAnswer.classList.remove('active');
                    if (otherIcon) otherIcon.style.transform = 'rotate(0deg)';
                });
                
                // Toggle current item
                if (!isActive) {
                    answer.classList.add('active');
                    if (icon) icon.style.transform = 'rotate(180deg)';
                }
            });
        }
    });
}

// Scroll Animations
function initializeScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animatedElements = document.querySelectorAll('.product-card, .feature, .info-card, .payment-card, .policy-card');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// Utility Functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Loading Animation
window.addEventListener('load', function() {
    const loader = document.querySelector('.loader');
    if (loader) {
        loader.style.opacity = '0';
        setTimeout(() => {
            loader.style.display = 'none';
        }, 300);
    }
});

// Back to Top Button
function createBackToTopButton() {
    const backToTop = document.createElement('button');
    backToTop.innerHTML = '<i class="fas fa-arrow-up"></i>';
    backToTop.className = 'back-to-top';
    backToTop.style.cssText = `
        position: fixed;
        bottom: 80px;
        left: 20px;
        width: 50px;
        height: 50px;
        background: #ff6b9d;
        color: white;
        border: none;
        border-radius: 50%;
        cursor: pointer;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 999;
        font-size: 1.2rem;
    `;

    document.body.appendChild(backToTop);

    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            backToTop.style.opacity = '1';
            backToTop.style.visibility = 'visible';
        } else {
            backToTop.style.opacity = '0';
            backToTop.style.visibility = 'hidden';
        }
    });

    backToTop.addEventListener('click', () => {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// Initialize back to top button
createBackToTopButton();
