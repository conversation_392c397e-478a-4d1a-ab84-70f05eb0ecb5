# متجر نيسا - متجر الأناقة النسائية 👗

متجر إلكتروني أنيق ومتكامل لبيع الملابس النسائية، مصمم خصيصاً ليناسب الذوق العربي والأنثوي مع لمسة تسويقية مميزة.

## 🌟 المميزات

- **تصميم أنثوي أنيق** - ألوان وردية وتدرجات ناعمة تناسب الذوق النسائي
- **متجاوب مع جميع الأجهزة** - يعمل بشكل مثالي على الهواتف والأجهزة اللوحية وأجهزة الكمبيوتر
- **تكامل مع واتساب** - إمكانية التواصل المباشر والطلب عبر واتساب
- **نظام تصفية متقدم** - تصفية المنتجات حسب الفئة والسعر والبحث
- **تصميم سريع التحميل** - محسن للأداء والسرعة
- **دعم اللغة العربية** - مصمم خصيصاً للمحتوى العربي مع دعم RTL

## 📁 هيكل المشروع

```
NISA/
├── index.html              # الصفحة الرئيسية
├── products.html           # صفحة المنتجات
├── shipping.html           # صفحة التوصيل والدفع
├── css/
│   └── style.css          # ملف التصميم الرئيسي
├── js/
│   └── script.js          # ملف JavaScript للوظائف التفاعلية
├── images/                # مجلد الصور
│   └── README.md          # تعليمات إضافة الصور
└── README.md              # هذا الملف
```

## 🚀 كيفية الاستخدام

### 1. إعداد الملفات
- تأكدي من وجود جميع الملفات في المجلد الصحيح
- ارفعي الملفات إلى خادم الويب أو افتحي `index.html` في المتصفح

### 2. إضافة الصور
- اذهبي إلى مجلد `images/`
- اقرئي ملف `README.md` الموجود في المجلد لمعرفة الصور المطلوبة
- أضيفي صور المنتجات بالأسماء المحددة

### 3. تخصيص المحتوى

#### تغيير معلومات المتجر:
- افتحي ملف `index.html`
- غيري اسم المتجر من "نيسا" إلى الاسم المطلوب
- حدثي الشعار والوصف

#### إضافة منتجات جديدة:
- افتحي ملف `products.html`
- انسخي أحد عناصر `.product-card`
- غيري المعلومات (الاسم، السعر، الوصف، الصورة)
- أضيفي المنتج الجديد في ملف `js/script.js` في قسم `productData`

#### تحديث معلومات التواصل:
- ابحثي عن `966500000000` في جميع الملفات
- غيري الرقم إلى رقم واتساب الخاص بك
- حدثي عنوان البريد الإلكتروني

### 4. تخصيص التصميم

#### تغيير الألوان:
- افتحي ملف `css/style.css`
- ابحثي عن `#ff6b9d` (اللون الوردي الرئيسي)
- غيري إلى اللون المطلوب
- ابحثي عن `#c44569` (اللون الوردي الداكن)
- غيري إلى تدرج أغمق من اللون الجديد

#### تغيير الخطوط:
- في ملف `css/style.css`
- ابحثي عن `'Tajawal'`
- غيري إلى الخط المطلوب (تأكدي من إضافة رابط الخط في HTML)

## 📱 الصفحات المتاحة

### 1. الصفحة الرئيسية (`index.html`)
- بانر ترحيبي جذاب
- عرض المنتجات المميزة
- قسم "لماذا نيسا؟"
- معلومات التواصل

### 2. صفحة المنتجات (`products.html`)
- عرض جميع المنتجات
- نظام تصفية وبحث
- عرض سريع للمنتجات
- أزرار واتساب لكل منتج

### 3. صفحة التوصيل والدفع (`shipping.html`)
- سياسة التوصيل
- طرق الدفع المتاحة
- سياسة الإرجاع والاستبدال
- الأسئلة الشائعة

## 🛠️ التخصيص المتقدم

### إضافة منتج جديد:

1. **في HTML** (`products.html`):
```html
<div class="product-card" data-category="dresses" data-price="299">
    <div class="product-image">
        <img src="images/new-product.jpg" alt="منتج جديد">
        <div class="product-overlay">
            <button class="quick-view" onclick="openProductModal('newProduct')">عرض سريع</button>
        </div>
    </div>
    <div class="product-info">
        <h3 class="product-name">اسم المنتج الجديد</h3>
        <p class="product-description">وصف المنتج</p>
        <p class="product-price">299 ريال</p>
        <button class="whatsapp-btn" onclick="contactWhatsApp('اسم المنتج', '299')">
            <i class="fab fa-whatsapp"></i> اطلبي عبر واتساب
        </button>
    </div>
</div>
```

2. **في JavaScript** (`js/script.js`):
```javascript
// أضيفي في قسم productData
newProduct: {
    name: 'اسم المنتج الجديد',
    description: 'وصف مفصل للمنتج...',
    price: '299 ريال',
    image: 'images/new-product.jpg',
    colors: ['#000', '#FFF', '#FF0000']
}
```

### تغيير رقم واتساب:
ابحثي عن `966500000000` في الملفات التالية وغيريه:
- `index.html`
- `products.html`
- `shipping.html`
- `js/script.js`

## 🎨 الألوان المستخدمة

- **الوردي الرئيسي:** `#ff6b9d`
- **الوردي الداكن:** `#c44569`
- **الرمادي الفاتح:** `#fafafa`
- **الرمادي المتوسط:** `#7f8c8d`
- **الأزرق الداكن:** `#2c3e50`
- **الأخضر (واتساب):** `#25d366`

## 📞 الدعم والمساعدة

إذا كنت تحتاجين مساعدة في:
- تخصيص التصميم
- إضافة ميزات جديدة
- حل المشاكل التقنية
- تحسين الأداء

يمكنك التواصل للحصول على الدعم التقني.

## 🔧 متطلبات التشغيل

- متصفح ويب حديث (Chrome, Firefox, Safari, Edge)
- خادم ويب (للنشر على الإنترنت)
- محرر نصوص لتعديل الملفات

## 📈 تحسينات مستقبلية

- إضافة نظام إدارة المخزون
- تكامل مع بوابات الدفع الإلكتروني
- نظام تقييم المنتجات
- برنامج نقاط الولاء
- تطبيق جوال

---

**تم تصميم هذا المتجر بعناية ليناسب احتياجات المرأة العربية العصرية ويوفر تجربة تسوق مميزة وأنيقة.**

💝 **نتمنى لك التوفيق في مشروعك التجاري!**
